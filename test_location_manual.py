#!/usr/bin/env python3
"""
Manual test script to verify location service functionality.
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

from app.services.location_service import LocationService

async def test_location_service():
    """Test the location service with a real API call."""
    service = LocationService()
    
    # Test with club ID 312 (from the example URL)
    print("Testing location service with club ID 312...")
    
    try:
        result = await service.get_location_detail("312")
        if result:
            print(f"Success! Club Title: {result.clubTitle}")
            print(f"Club ID: {result.clubId}")
        else:
            print("No result returned")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test with multiple club IDs
    print("\nTesting with multiple club IDs...")
    try:
        locations = await service.get_club_locations(["312", "313"])
        print(f"Locations: {locations}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_location_service())
