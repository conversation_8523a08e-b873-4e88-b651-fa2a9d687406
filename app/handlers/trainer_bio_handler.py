import difflib
import os
from typing import AsyncGenerator, List, Optional, Union

from ai_agent_utils.agent.performance_tracking import track_time
from ai_agent_utils.logging import logger
from ai_agent_utils.services.openai import OpenAIClient
from jinja2 import Environment, FileSystemLoader

from app.models.trainer_bio import TrainerBio
from app.repositories.trainer_bio import search_trainer_bio_by_name
from app.services.location_service import LocationService

current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
env = Environment(loader=FileSystemLoader(f"{parent_dir}/templates"))


def calculate_name_similarity(requested_name: str, trainer_bio: TrainerBio) -> float:
    """
    Calculate similarity score between requested name and trainer's name.
    Returns a score between 0 and 1, where 1 is a perfect match.
    """
    requested_name_lower = requested_name.lower().strip()
    trainer_full_name = f"{trainer_bio.firstName} {trainer_bio.lastName}".lower().strip()

    similarity = difflib.SequenceMatcher(None, requested_name_lower, trainer_full_name).ratio()

    return similarity


def filter_and_sort_trainer_results(
    requested_name: str,
    trainer_bios: List[TrainerBio],
    min_similarity: float = 0.5,
    prioritize_exact_matches: bool = True
) -> List[TrainerBio]:
    """
    Filter and sort trainer bios by name similarity to the requested name.
    Returns results above the minimum similarity threshold, sorted by similarity score.
    If prioritize_exact_matches is True, returns only exact matches when available.
    """
    if not trainer_bios:
        return []

    scored_results = []
    for bio in trainer_bios:
        similarity = calculate_name_similarity(requested_name, bio)
        if similarity >= min_similarity:
            scored_results.append((similarity, bio))

    if not scored_results:
        return []

    scored_results.sort(key=lambda x: x[0], reverse=True)

    if prioritize_exact_matches:
        exact_matches = [bio for score, bio in scored_results if score == 1.0]
        if exact_matches:
            return exact_matches

    return [bio for _, bio in scored_results]


async def enrich_trainer_bios_with_locations(trainer_bios: List[TrainerBio]) -> List[dict]:
    """
    Enrich trainer bios with club location information.

    Args:
        trainer_bios: List of TrainerBio objects

    Returns:
        List of dictionaries with trainer bio data including club locations
    """
    location_service = LocationService()
    enriched_bios = []

    for bio in trainer_bios:
        bio_dict = bio.model_dump()

        # Fetch club locations for this trainer
        if bio.clubIds:
            club_locations = await location_service.get_club_locations(bio.clubIds)
            bio_dict['club_locations'] = list(club_locations.values())
        else:
            bio_dict['club_locations'] = []

        enriched_bios.append(bio_dict)

    return enriched_bios


@track_time("ai-members-scheduling-agent", os.getenv("CHAT_DEPLOYMENT_NAME"))
async def trainer_bio_handler(
    question: str,
    history: list,
    scratch_pad: dict,
    extracted_trainer_name: Optional[str],
    stream: bool = False,
) -> Union[AsyncGenerator[str, None], dict]:
    logger.info(
        "Starting trainer_bio_handler",
        facets={
            "question": question,
            "extracted_trainer_name": extracted_trainer_name,
            "stream": stream,
        },
    )

    if not extracted_trainer_name:
        answer = "I couldn't quite catch the trainer's name you're asking about. Could you please tell me their name again? 😊"

        async def gen_missing_name_response():
            yield answer

        if stream:
            return {
                "answer": gen_missing_name_response(),
                "references": [],
                "path": "trainer_bio_query_name_missing",
                "metadata": None,
            }

        return {
            "answer": answer,
            "references": [],
            "path": "trainer_bio_query_name_missing",
            "metadata": None,
        }

    trainer_bios_data = await search_trainer_bio_by_name(extracted_trainer_name)

    trainer_bios_renderable = None
    total_results_count = 0
    filtered_results_count = 0

    if trainer_bios_data:
        total_results_count = len(trainer_bios_data)

        filtered_bios_data = filter_and_sort_trainer_results(
            extracted_trainer_name,
            trainer_bios_data,
            min_similarity=0.6,
            prioritize_exact_matches=True
        )
        filtered_results_count = len(filtered_bios_data)

        logger.info(
            f"Filtered trainer search results: {total_results_count} -> {filtered_results_count} results for '{extracted_trainer_name}'",
            facets={
                "trainer_name": extracted_trainer_name,
                "original_count": total_results_count,
                "filtered_count": filtered_results_count,
                "has_exact_match": filtered_results_count > 0 and calculate_name_similarity(extracted_trainer_name, filtered_bios_data[0]) == 1.0,
            },
        )

        # Limit to first 10 results to prevent OpenAI API timeouts with large prompts
        limited_bios_data = filtered_bios_data[:10]
        trainer_bios_renderable = await enrich_trainer_bios_with_locations(limited_bios_data)

    template = env.get_template("trainer_bio_response.jinja2")
    system_prompt = template.render(
        question=question,
        history=history,
        trainer_bios=trainer_bios_renderable,
        requested_trainer_name=extracted_trainer_name,
        user_data=scratch_pad,
        total_results_count=filtered_results_count,
        results_were_limited=(filtered_results_count > 10),
    )

    openai_response = await OpenAIClient.chat_create(
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": question},
        ],
        stream=stream,
        temperature=0.1,
    )

    final_answer = openai_response
    if not stream and openai_response.choices:
        final_answer = openai_response.choices[0].message.content

    references = []

    return {
        "answer": final_answer,
        "references": references,
        "path": "trainer_bio_query_result",
        "metadata": None,
    }
