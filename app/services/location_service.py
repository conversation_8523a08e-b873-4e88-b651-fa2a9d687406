import os
from typing import Dict, List, Optional

import httpx
from ai_agent_utils.logging import logger

from app.models.location import LocationDetail


class LocationService:
    """Service for fetching location details from the location API."""
    
    def __init__(self):
        self.api_key = os.getenv("LOCATION_API_KEY")
        self.base_url = "https://api.lifetime.life/data360/data360-api/location"
        
    async def get_location_detail(self, club_id: str) -> Optional[LocationDetail]:
        """
        Fetch location detail for a specific club ID.
        
        Args:
            club_id: The club ID to fetch details for
            
        Returns:
            LocationDetail object if successful, None otherwise
        """
        if not self.api_key:
            logger.error("LOCATION_API_KEY not found in environment variables")
            return None
            
        url = f"{self.base_url}/{club_id}/detail"
        params = {"apiKey": self.api_key}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                response.raise_for_status()

                data = response.json()
                return LocationDetail(**data)
                
        except httpx.HTTPError as e:
            logger.error(f"HTTP error fetching location for club {club_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error fetching location for club {club_id}: {e}")
            return None
    
    async def get_club_locations(self, club_ids: List[str]) -> Dict[str, str]:
        """
        Fetch location details for multiple club IDs.
        
        Args:
            club_ids: List of club IDs to fetch details for
            
        Returns:
            Dictionary mapping club_id to club_title
        """
        locations = {}
        
        for club_id in club_ids:
            location_detail = await self.get_location_detail(club_id)
            if location_detail and location_detail.clubTitle:
                locations[club_id] = location_detail.clubTitle
            else:
                logger.warning(f"Could not fetch location for club ID: {club_id}")
                
        return locations
